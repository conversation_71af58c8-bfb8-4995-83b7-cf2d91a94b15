import { Logger } from '../services/logger';
import { calculateDistance, calculateBearing, calculateETA } from '../utils/geo';

interface RideParticipant {
  id: string;
  type: 'driver' | 'passenger';
  socket: WebSocket;
  joinedAt: number;
}

interface LocationUpdate {
  lat: number;
  lng: number;
  heading?: number;
  speed?: number;
  accuracy?: number;
  timestamp: number;
}

interface RideState {
  tripId: string;
  status: 'requested' | 'accepted' | 'arrived' | 'started' | 'completed' | 'cancelled';
  driver?: {
    id: string;
    name: string;
    vehicleInfo: {
      make: string;
      model: string;
      color: string;
      plateNumber: string;
    };
  };
  passenger: {
    id: string;
    name: string;
  };
  locations: {
    pickup: { lat: number; lng: number; address?: string };
    destination: { lat: number; lng: number; address?: string };
    current?: LocationUpdate;
  };
  route?: {
    distance: number; // in meters
    duration: number; // in seconds
    polyline: string;
  };
  startTime?: number;
  endTime?: number;
  chatEnabled: boolean;
}

interface ChatMessage {
  id: string;
  senderId: string;
  senderType: 'driver' | 'passenger';
  message: string;
  timestamp: number;
}

interface WebSocketMessage {
  type: string;
  data?: any;
}

export class RideTracker {
  private participants: Map<WebSocket, RideParticipant> = new Map();
  private rideState: RideState | null = null;
  private chatHistory: ChatMessage[] = [];
  private locationHistory: LocationUpdate[] = [];
  private logger: Logger;

  constructor(private state: DurableObjectState, private env: Env) {
    this.logger = new Logger(env);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    if (path === '/websocket') {
      return this.handleWebSocketUpgrade(request);
    }

    if (path === '/initialize' && request.method === 'POST') {
      return this.handleInitialize(request);
    }

    if (path === '/state' && request.method === 'GET') {
      return this.handleGetState();
    }

    if (path === '/update-status' && request.method === 'POST') {
      return this.handleUpdateStatus(request);
    }

    return new Response('Not Found', { status: 404 });
  }

  private async handleWebSocketUpgrade(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (!upgradeHeader || upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 426 });
    }

    const userId = request.headers.get('X-User-Id');
    const userType = request.headers.get('X-User-Type') as 'driver' | 'passenger';
    const tripId = request.headers.get('X-Trip-Id');

    if (!userId || !userType || !tripId) {
      return new Response('Missing required headers', { status: 401 });
    }

    // Verify this is the correct trip
    if (this.rideState && this.rideState.tripId !== tripId) {
      return new Response('Invalid trip ID', { status: 403 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    const participant: RideParticipant = {
      id: userId,
      type: userType,
      socket: server,
      joinedAt: Date.now()
    };

    this.participants.set(server, participant);

    // Use hibernation API instead of event listeners
    this.state.acceptWebSocket(server);

    // Send initial state
    server.send(JSON.stringify({
      type: 'connected',
      data: {
        rideState: this.rideState,
        participants: Array.from(this.participants.values()).map(p => p.id).filter(id => id !== userId),
        chatHistory: this.chatHistory.slice(-50) // Last 50 messages
      }
    }));

    // Notify other participants
    this.broadcast({
      type: 'participant_joined',
      data: { userId, userType }
    }, server);

    return new Response(null, {
      status: 101,
      webSocket: client
    });
  }

  // WebSocket Hibernation API methods
  async webSocketMessage(ws: WebSocket, message: string): Promise<void> {
    try {
      const parsedMessage = JSON.parse(message) as WebSocketMessage;
      await this.handleWebSocketMessage(ws, parsedMessage);
    } catch (error) {
      this.logger.error('Error handling WebSocket message', { error });
      ws.send(JSON.stringify({ type: 'error', message: 'Invalid message format' }));
    }
  }

  async webSocketClose(ws: WebSocket, code: number, reason: string, wasClean: boolean): Promise<void> {
    this.handleParticipantDisconnect(ws);
  }

  private async handleWebSocketMessage(ws: WebSocket, message: WebSocketMessage): Promise<void> {
    const participant = this.participants.get(ws);
    if (!participant || !this.rideState) return;

    switch (message.type) {
      case 'location_update':
        await this.handleLocationUpdate(participant.id, participant.type, message.data);
        break;

      case 'chat_message':
        if (this.rideState.chatEnabled) {
          await this.handleChatMessage(participant.id, participant.type, message.data);
        }
        break;

      case 'eta_request':
        await this.sendETAUpdate(ws);
        break;

      case 'trip_action':
        await this.handleTripAction(participant.id, participant.type, message.data);
        break;
    }
  }

  private async handleLocationUpdate(userId: string, userType: string, location: LocationUpdate): Promise<void> {
    if (!this.rideState) return;

    // Only drivers can send location updates
    if (userType !== 'driver') return;

    // Update current location
    this.rideState.locations.current = location;
    this.locationHistory.push(location);

    // Keep only last 100 location updates
    if (this.locationHistory.length > 100) {
      this.locationHistory = this.locationHistory.slice(-100);
    }

    // Calculate distance to pickup/destination
    let distanceInfo: any = {};

    if (this.rideState.status === 'accepted' || this.rideState.status === 'arrived') {
      // Calculate distance to pickup
      const distance = calculateDistance(
        { lat: location.lat, lon: location.lng },
        { lat: this.rideState.locations.pickup.lat, lon: this.rideState.locations.pickup.lng }
      );
      const bearing = calculateBearing(
        { lat: location.lat, lon: location.lng },
        { lat: this.rideState.locations.pickup.lat, lon: this.rideState.locations.pickup.lng }
      );
      const eta = calculateETA(distance, location.speed || 30);

      distanceInfo = {
        toPickup: {
          distance: distance * 1000, // Convert to meters
          bearing,
          eta
        }
      };
    } else if (this.rideState.status === 'started') {
      // Calculate distance to destination
      const distance = calculateDistance(
        { lat: location.lat, lon: location.lng },
        { lat: this.rideState.locations.destination.lat, lon: this.rideState.locations.destination.lng }
      );
      const bearing = calculateBearing(
        { lat: location.lat, lon: location.lng },
        { lat: this.rideState.locations.destination.lat, lon: this.rideState.locations.destination.lng }
      );
      const eta = calculateETA(distance, location.speed || 30);

      distanceInfo = {
        toDestination: {
          distance: distance * 1000, // Convert to meters
          bearing,
          eta
        }
      };
    }

    // Broadcast location update to all participants
    this.broadcast({
      type: 'driver_location_update',
      data: {
        location,
        ...distanceInfo
      }
    });
  }

  private async handleChatMessage(userId: string, userType: string, messageData: { message: string }): Promise<void> {
    if (!this.rideState || !this.rideState.chatEnabled) return;

    const chatMessage: ChatMessage = {
      id: crypto.randomUUID(),
      senderId: userId,
      senderType: userType as 'driver' | 'passenger',
      message: messageData.message.substring(0, 500), // Limit message length
      timestamp: Date.now()
    };

    this.chatHistory.push(chatMessage);

    // Keep only last 100 messages
    if (this.chatHistory.length > 100) {
      this.chatHistory = this.chatHistory.slice(-100);
    }

    // Broadcast to all participants
    this.broadcast({
      type: 'chat_message',
      data: chatMessage
    });
  }

  private async handleTripAction(userId: string, userType: string, action: { type: string; data?: any }): Promise<void> {
    if (!this.rideState) return;

    // Validate user can perform action
    if (userType === 'driver' && this.rideState.driver?.id !== userId) return;
    if (userType === 'passenger' && this.rideState.passenger.id !== userId) return;

    switch (action.type) {
      case 'arrive_at_pickup':
        if (userType === 'driver' && this.rideState.status === 'accepted') {
          this.rideState.status = 'arrived';
          this.broadcast({
            type: 'status_update',
            data: { status: 'arrived', timestamp: Date.now() }
          });
        }
        break;

      case 'start_trip':
        if (userType === 'driver' && this.rideState.status === 'arrived') {
          this.rideState.status = 'started';
          this.rideState.startTime = Date.now();
          this.broadcast({
            type: 'status_update',
            data: { status: 'started', timestamp: Date.now() }
          });
        }
        break;

      case 'complete_trip':
        if (userType === 'driver' && this.rideState.status === 'started') {
          this.rideState.status = 'completed';
          this.rideState.endTime = Date.now();
          this.broadcast({
            type: 'status_update',
            data: { status: 'completed', timestamp: Date.now() }
          });

          // Close all connections after a delay
          setTimeout(() => {
            this.closeAllConnections();
          }, 5000);
        }
        break;

      case 'cancel_trip':
        if (this.rideState.status !== 'completed') {
          this.rideState.status = 'cancelled';
          this.broadcast({
            type: 'status_update',
            data: {
              status: 'cancelled',
              cancelledBy: userType,
              timestamp: Date.now()
            }
          });

          // Close all connections after a delay
          setTimeout(() => {
            this.closeAllConnections();
          }, 5000);
        }
        break;
    }
  }

  private async sendETAUpdate(ws: WebSocket): Promise<void> {
    const participant = this.participants.get(ws);
    if (!participant || !this.rideState || !this.rideState.locations.current) return;

    let eta = null;

    if (this.rideState.status === 'accepted' || this.rideState.status === 'arrived') {
      const distance = calculateDistance(
        { lat: this.rideState.locations.current.lat, lon: this.rideState.locations.current.lng },
        { lat: this.rideState.locations.pickup.lat, lon: this.rideState.locations.pickup.lng }
      );
      eta = calculateETA(distance, this.rideState.locations.current.speed || 30);
    }

    ws.send(JSON.stringify({
      type: 'eta_update',
      data: { eta, timestamp: Date.now() }
    }));
  }

  private async handleInitialize(request: Request): Promise<Response> {
    try {
      const data = await request.json() as {
        tripId: string;
        status: string;
        driver?: any;
        passenger: any;
        locations: any;
        route?: any;
        chatEnabled?: boolean;
      };

      this.rideState = {
        tripId: data.tripId,
        status: data.status as RideState['status'],
        driver: data.driver,
        passenger: data.passenger,
        locations: data.locations,
        route: data.route,
        chatEnabled: data.chatEnabled ?? true
      };

      return new Response(JSON.stringify({
        success: true,
        data: { initialized: true }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      this.logger.error('Error initializing ride tracker', { error });
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to initialize ride tracker'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  private async handleGetState(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: {
        rideState: this.rideState,
        participantCount: this.participants.size,
        locationHistoryCount: this.locationHistory.length,
        chatHistoryCount: this.chatHistory.length
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleUpdateStatus(request: Request): Promise<Response> {
    try {
      const { status } = await request.json() as { status: RideState['status'] };

      if (!this.rideState) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Ride not initialized'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      this.rideState.status = status;

      // Broadcast status update
      this.broadcast({
        type: 'status_update',
        data: { status, timestamp: Date.now() }
      });

      return new Response(JSON.stringify({
        success: true,
        data: { status }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      this.logger.error('Error updating status', { error });
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to update status'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  private broadcast(message: WebSocketMessage, excludeWs?: WebSocket): void {
    const messageStr = JSON.stringify(message);

    for (const [ws, participant] of this.participants) {
      if (ws !== excludeWs) {
        try {
          ws.send(messageStr);
        } catch (error) {
          this.logger.error('Error broadcasting message', { error, userId: participant.id });
        }
      }
    }
  }

  private handleParticipantDisconnect(userId: string): void {
    const participant = this.participants.get(userId);
    if (!participant) return;

    this.participants.delete(userId);

    // Notify other participants
    this.broadcast({
      type: 'participant_left',
      data: { userId, userType: participant.type }
    });
  }

  private closeAllConnections(): void {
    for (const [userId, participant] of this.participants) {
      try {
        participant.socket.close(1000, 'Ride completed');
      } catch (error) {
        this.logger.error('Error closing connection', { error, userId });
      }
    }
    this.participants.clear();
  }
}