import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { getPrisma } from '../../services/database';
import { ConfigurationService } from '../../services/configuration';
import { Logger } from '../../services/logger';
import { requireAuth } from '../../middleware/auth';
import { requireConfigModificationRole, requireAdminListRole } from '../../middleware/admin-auth';
import {
  TripConfiguration,
  DEFAULT_TRIP_CONFIGURATION,
  CONFIGURATION_KEYS,
  CONFIGURATION_CATEGORIES
} from '../../types/configuration';
import { createErrorResponse } from '../../utils/error-response';
import type { Bindings } from '../../types/bindings';

const configurations = new Hono<{ Bindings: Bindings }>();

// Apply admin authentication to all routes
configurations.use('*', requireAuth);
configurations.use('*', requireAdminListRole());

// Validation schemas
const tripConfigurationSchema = z.object({
  costPerKilometer: z.number().min(0, 'Must be a positive number'),
  costPerHour: z.number().min(0, 'Must be a positive number'),
  minimumTripCost: z.number().min(0, 'Must be a positive number'),
  waitTimeAfterExtraPayment: z.number().min(0, 'Must be a positive number'),
  costPerExtraWaitChunk: z.number().min(0, 'Must be a positive number'),
  cancelCostPreStart: z.number().min(0, 'Must be a positive number'),
  nearbyDriverListedRadiusMeters: z.number().min(100, 'Must be at least 100 meters'),
});

const updateConfigurationSchema = z.object({
  value: z.any(),
  description: z.string().optional(),
  category: z.enum([
    CONFIGURATION_CATEGORIES.GENERAL,
    CONFIGURATION_CATEGORIES.TRIP,
    CONFIGURATION_CATEGORIES.PRICING,
    CONFIGURATION_CATEGORIES.FEATURES,
    CONFIGURATION_CATEGORIES.LIMITS,
  ]).optional(),
  changeReason: z.string().optional(),
});

const validateConfigurationSchema = z.object({
  value: z.any(),
});

/**
 * GET /api/admin/configurations - List all configurations
 */
configurations.get('/', async (c) => {
  const logger = c.get('logger') || new Logger(crypto.randomUUID());
  const user = c.get('user');
  const adminUid = user?.uid;

  try {
    logger.info('Fetching all configurations', { adminUid });

    const category = c.req.query('category');
    const isActive = c.req.query('active');

    const prisma = getPrisma(c.env.DB);
    const configService = new ConfigurationService(prisma, logger);

    const configurations = await configService.getAllConfigurations({
      ...(category && { category }),
      ...(isActive !== undefined && { isActive: isActive === 'true' }),
    });

    logger.info('Configurations retrieved successfully', {
      count: configurations.length,
      adminUid
    });

    return c.json({
      success: true,
      data: configurations,
      meta: {
        count: configurations.length,
        filters: { category, isActive },
      },
    });

  } catch (error) {
    logger.error('Failed to fetch configurations', { error, adminUid });
    return createErrorResponse(
      c,
      'CONFIGURATION_002',
      'Failed to fetch configurations',
      500,
      { service: 'admin-config' }
    );
  }
});

/**
 * GET /api/admin/configurations/:key - Get specific configuration
 */
configurations.get('/:key', async (c) => {
  const logger = c.get('logger') || new Logger(crypto.randomUUID());
  const user = c.get('user');
  const adminUid = user?.uid;
  const key = c.req.param('key');

  try {
    logger.info('Fetching configuration', { key, adminUid });

    const prisma = getPrisma(c.env.DB);
    const configService = new ConfigurationService(prisma, logger);

    const configuration = await configService.getConfiguration(key);

    if (!configuration) {
      return createErrorResponse(
        c,
        'CONFIGURATION_003',
        'Configuration not found',
        404,
        { key }
      );
    }

    logger.info('Configuration retrieved successfully', { key, adminUid });

    return c.json({
      success: true,
      data: configuration,
    });

  } catch (error) {
    logger.error('Failed to fetch configuration', { error, key, adminUid });
    return createErrorResponse(
      c,
      'CONFIGURATION_004',
      'Failed to fetch configuration',
      500,
      { service: 'admin-config', key }
    );
  }
});

/**
 * PUT /api/admin/configurations/:key - Update configuration
 */
configurations.put(
  '/:key',
  requireConfigModificationRole(),
  zValidator('json', updateConfigurationSchema),
  async (c) => {
    const logger = c.get('logger') || new Logger(crypto.randomUUID());
    const user = c.get('user');
    const adminUid = user?.uid;
    const key = c.req.param('key');
    const { value, description, category, changeReason } = c.req.valid('json');

    try {
      logger.info('Updating configuration', { key, adminUid });

      const prisma = getPrisma(c.env.DB);
      const configService = new ConfigurationService(prisma, logger);

      // Special validation for trip configuration
      if (key === CONFIGURATION_KEYS.TRIP_CONFIGURATION) {
        const tripConfigValidation = tripConfigurationSchema.safeParse(value);
        if (!tripConfigValidation.success) {
          return createErrorResponse(
            c,
            ErrorCode.CONFIGURATION_005,
            'Invalid trip configuration format',
            400,
            {
              validationErrors: tripConfigValidation.error.errors,
              key
            }
          );
        }
      }

      // Validate configuration
      const validation = await configService.validateConfiguration(key, value);
      if (!validation.isValid) {
        return createErrorResponse(
          c,
          ErrorCode.CONFIGURATION_006,
          'Configuration validation failed',
          400,
          {
            validation,
            key
          }
        );
      }

      // Update configuration
      const updatedConfig = await configService.updateConfiguration(
        key,
        value,
        adminUid,
        { description, category, changeReason }
      );

      logger.info('Configuration updated successfully', {
        key,
        version: updatedConfig.version,
        adminUid
      });

      return c.json({
        success: true,
        data: updatedConfig,
        meta: {
          validation,
        },
      });

    } catch (error) {
      logger.error('Failed to update configuration', { error, key, adminUid });
      return createErrorResponse(
        c,
        ErrorCode.CONFIGURATION_007,
        'Failed to update configuration',
        500,
        { service: 'admin-config', key }
      );
    }
  }
);

/**
 * GET /api/admin/configurations/:key/history - Configuration change history
 */
configurations.get('/:key/history', async (c) => {
  const logger = c.get('logger') || new Logger(crypto.randomUUID());
  const user = c.get('user');
  const adminUid = user?.uid;
  const key = c.req.param('key');

  try {
    logger.info('Fetching configuration history', { key, adminUid });

    const limit = parseInt(c.req.query('limit') || '50');
    const offset = parseInt(c.req.query('offset') || '0');

    const prisma = getPrisma(c.env.DB);
    const configService = new ConfigurationService(prisma, logger);

    const history = await configService.getConfigurationHistory(key, { limit, offset });

    logger.info('Configuration history retrieved successfully', {
      key,
      count: history.length,
      adminUid
    });

    return c.json({
      success: true,
      data: history,
      meta: {
        count: history.length,
        limit,
        offset,
        key,
      },
    });

  } catch (error) {
    logger.error('Failed to fetch configuration history', { error, key, adminUid });
    return createErrorResponse(
      c,
      ErrorCode.CONFIGURATION_008,
      'Failed to fetch configuration history',
      500,
      { service: 'admin-config', key }
    );
  }
});

/**
 * POST /api/admin/configurations/:key/validate - Validate configuration
 */
configurations.post(
  '/:key/validate',
  zValidator('json', validateConfigurationSchema),
  async (c) => {
    const logger = c.get('logger') || new Logger(crypto.randomUUID());
    const user = c.get('user');
    const adminUid = user?.uid;
    const key = c.req.param('key');
    const { value } = c.req.valid('json');

    try {
      logger.info('Validating configuration', { key, adminUid });

      const prisma = getPrisma(c.env.DB);
      const configService = new ConfigurationService(prisma, logger);

      const validation = await configService.validateConfiguration(key, value);

      logger.info('Configuration validation completed', {
        key,
        isValid: validation.isValid,
        errorCount: validation.errors.length,
        adminUid
      });

      return c.json({
        success: true,
        data: validation,
      });

    } catch (error) {
      logger.error('Failed to validate configuration', { error, key, adminUid });
      return createErrorResponse(
        c,
        ErrorCode.CONFIGURATION_009,
        'Failed to validate configuration',
        500,
        { service: 'admin-config', key }
      );
    }
  }
);

/**
 * GET /api/admin/configurations/:key/impact - Analyze change impact
 */
configurations.post(
  '/:key/impact',
  zValidator('json', validateConfigurationSchema),
  async (c) => {
    const logger = c.get('logger') || new Logger(crypto.randomUUID());
    const user = c.get('user');
    const adminUid = user?.uid;
    const key = c.req.param('key');
    const { value } = c.req.valid('json');

    try {
      logger.info('Analyzing configuration impact', { key, adminUid });

      const prisma = getPrisma(c.env.DB);
      const configService = new ConfigurationService(prisma, logger);

      const impact = await configService.analyzeConfigurationImpact(key, value);

      logger.info('Configuration impact analysis completed', {
        key,
        estimatedImpact: impact.estimatedImpact,
        priceChangePercentage: impact.priceChangePercentage,
        adminUid
      });

      return c.json({
        success: true,
        data: impact,
      });

    } catch (error) {
      logger.error('Failed to analyze configuration impact', { error, key, adminUid });
      return createErrorResponse(
        c,
        ErrorCode.CONFIGURATION_010,
        'Failed to analyze configuration impact',
        500,
        { service: 'admin-config', key }
      );
    }
  }
);

export { configurations };